buildscript {
    

    repositories {
        mavenLocal()
        mavenCentral()
        gradlePluginPortal()
        maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.2'
        classpath 'com.mobidevelop.robovm:robovm-gradle-plugin:2.3.21'
        classpath 'org.multi-os-engine:moe-gradle:1.8.0'

    }
}

allprojects {
    apply plugin: "eclipse"

    version = '1.0'
    ext {
        appName = "Army2"
        gdxVersion = '1.12.1'
        roboVMVersion = '2.3.21'
        box2DLightsVersion = '1.5'
        ashleyVersion = '1.7.4'
        aiVersion = '1.8.2'
        gdxControllersVersion = '2.2.1'
    }

    repositories {
        mavenLocal()
        mavenCentral()
        google()
        gradlePluginPortal()
        maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
        maven { url "https://oss.sonatype.org/content/repositories/releases/" }
        maven { url "https://jitpack.io" }
    }
}

project(":desktop") {
    apply plugin: "java-library"


    dependencies {
        implementation project(":core")
        api "com.badlogicgames.gdx:gdx-backend-lwjgl3:$gdxVersion"
        api "com.badlogicgames.gdx:gdx-platform:$gdxVersion:natives-desktop"
        api "com.badlogicgames.gdx:gdx-box2d-platform:$gdxVersion:natives-desktop"
        
    }
}

project(":android") {
    apply plugin: "com.android.application"

    configurations { natives }

    dependencies {
        implementation project(":core")
        api "com.badlogicgames.gdx:gdx-backend-android:$gdxVersion"
        natives "com.badlogicgames.gdx:gdx-platform:$gdxVersion:natives-armeabi-v7a"
        natives "com.badlogicgames.gdx:gdx-platform:$gdxVersion:natives-arm64-v8a"
        natives "com.badlogicgames.gdx:gdx-platform:$gdxVersion:natives-x86"
        natives "com.badlogicgames.gdx:gdx-platform:$gdxVersion:natives-x86_64"
        api "com.badlogicgames.gdx:gdx-box2d:$gdxVersion"
        natives "com.badlogicgames.gdx:gdx-box2d-platform:$gdxVersion:natives-armeabi-v7a"
        natives "com.badlogicgames.gdx:gdx-box2d-platform:$gdxVersion:natives-arm64-v8a"
        natives "com.badlogicgames.gdx:gdx-box2d-platform:$gdxVersion:natives-x86"
        natives "com.badlogicgames.gdx:gdx-box2d-platform:$gdxVersion:natives-x86_64"
        
    }
}

project(":ios") {
    apply plugin: "java-library"
    apply plugin: "robovm"


    dependencies {
        implementation project(":core")
        api "com.mobidevelop.robovm:robovm-rt:$roboVMVersion"
        api "com.mobidevelop.robovm:robovm-cocoatouch:$roboVMVersion"
        api "com.badlogicgames.gdx:gdx-backend-robovm:$gdxVersion"
        api "com.badlogicgames.gdx:gdx-platform:$gdxVersion:natives-ios"
        api "com.badlogicgames.gdx:gdx-box2d-platform:$gdxVersion:natives-ios"
        
    }
}

project(":core") {
    apply plugin: "java-library"


    dependencies {
        api "com.badlogicgames.gdx:gdx:$gdxVersion"
        api "com.badlogicgames.gdx:gdx-box2d:$gdxVersion"
        
    }
}
